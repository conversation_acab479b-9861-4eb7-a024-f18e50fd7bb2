"""
AKShare data fetching tasks
"""

import datetime

from sqlalchemy import insert
from sqlalchemy.dialects.postgresql import insert as pg_insert

from app.celery_app import celery_app
from app.db.session import SessionLocal
from app.models.ak_stock_zh_a_hist_hfq_daily import AkStockZhAHistHFQDaily
from app.models.ak_stock_zh_a_hist_qfq_daily import AkStockZhAHistQFQDaily
from app.models.ak_stock_zh_a_hist_wfq_daily import AkStockZhAHistWFQDaily
from app.models.ak_stock_zh_a_spot_em import AkStockZhASpotEm
from app.services.akshare_fetcher import (fetch_stock_zh_a_hist_daily,
                                          fetch_stock_zh_a_spot_em)


@celery_app.task(queue="akshare_queue")
def fetch_and_store_stock_zh_a_spot_em():
    """
    获取A股实时行情数据并存储到数据库
    """
    try:
        # 获取数据
        df = fetch_stock_zh_a_spot_em()

        if df is not None and not df.empty:
            # 重命名列以匹配数据库模型字段
            column_mapping = {
                "序号": "sequence_number",
                "代码": "code",
                "名称": "name",
                "最新价": "latest_price",
                "涨跌幅": "change_percent",
                "涨跌额": "change_amount",
                "成交量": "volume",
                "成交额": "turnover",
                "振幅": "amplitude",
                "最高": "high",
                "最低": "low",
                "今开": "open",
                "昨收": "prev_close",
                "量比": "volume_ratio",
                "换手率": "turnover_rate",
                "市盈率-动态": "pe_ratio_dynamic",
                "市净率": "pb_ratio",
                "总市值": "total_market_value",
                "流通市值": "circulating_market_value",
                "涨速": "rise_speed",
                "5分钟涨跌": "five_min_change",
                "60日涨跌幅": "sixty_day_change",
                "年初至今涨跌幅": "ytd_change",
            }

            df = df.rename(columns=column_mapping)

            # 转换为字典列表
            records = df.to_dict(orient="records")

            # 存储到数据库，同步方式
            db = SessionLocal()
            try:
                db.execute(insert(AkStockZhASpotEm), records)
                db.commit()
            finally:
                db.close()

            return f"Successfully stored {len(records)} stock records"
        return "No data fetched or empty dataframe"
    except Exception as e:
        return f"Error: {str(e)}"


@celery_app.task(queue="akshare_queue", rate_limit="1/8s")
def fetch_and_store_stock_zh_a_hist_daily(symbol, adjust=""):
    """
    获取A股历史行情数据并存储到数据库

    Args:
        symbol: 股票代码，如 '600859'
        adjust: 复权类型，"qfq" 前复权，"hfq" 后复权，"" 不复权
    """
    try:
        end_date = datetime.datetime.now().strftime("%Y%m%d")

        # 获取数据
        df = fetch_stock_zh_a_hist_daily(symbol, "19800101", end_date, adjust)

        if df is not None and not df.empty:
            # 重命名列以匹配数据库模型字段
            column_mapping = {
                "日期": "date",
                "股票代码": "code",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "turnover",
                "振幅": "amplitude",
                "涨跌幅": "change_percent",
                "涨跌额": "change_amount",
                "换手率": "turnover_rate",
            }

            df = df.rename(columns=column_mapping)

            # 转换为字典列表
            records = df.to_dict(orient="records")

            # 根据adjust参数选择对应的模型
            if adjust == "qfq":
                model = AkStockZhAHistQFQDaily
            elif adjust == "hfq":
                model = AkStockZhAHistHFQDaily
            else:  # 默认无复权
                model = AkStockZhAHistWFQDaily

            # 存储到数据库，使用PostgreSQL的ON CONFLICT DO NOTHING
            db = SessionLocal()
            try:
                # 使用PostgreSQL的upsert功能
                stmt = pg_insert(model).values(records)
                stmt = stmt.on_conflict_do_nothing(index_elements=["code", "date"])
                db.execute(stmt)
                db.commit()
            finally:
                db.close()

            return f"Successfully stored {len(records)} {adjust} stock records for {symbol}"
        return f"No data fetched or empty dataframe for {symbol}"
    except Exception as e:
        return f"Error: {str(e)}"


if __name__ == "__main__":
    fetch_and_store_stock_zh_a_hist_daily("600859", "")
