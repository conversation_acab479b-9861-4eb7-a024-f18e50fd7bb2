#!/usr/bin/env python
"""
创建所有股票历史数据获取任务的脚本

执行逻辑：
1. 先执行SQL查询获取全部的股票代码：SELECT DISTINCT code FROM ak_stock_zh_a_spot_em
2. 对每一个code都创建3个task：
   - fetch_and_store_stock_zh_a_hist_daily task，symbol参数填写code，adjust设置为""
   - fetch_and_store_stock_zh_a_hist_daily task，symbol参数填写code，adjust设置为"qfq"
   - fetch_and_store_stock_zh_a_hist_daily task，symbol参数填写code，adjust设置为"hfq"
"""

import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text

from app.celery_app import celery_app
from app.db.session import SessionLocal


def create_all_stock_hist_tasks():
    """
    创建所有股票的历史数据获取任务

    执行逻辑：
    1. 从数据库获取所有股票代码
    2. 为每个代码创建3个任务：不复权、前复权、后复权
    """
    try:
        print("开始创建所有股票历史数据获取任务...")

        # 获取数据库会话
        db = SessionLocal()
        try:
            # 执行SQL查询获取所有股票代码
            print("正在查询数据库获取所有股票代码...")
            result = db.execute(
                text(
                    "SELECT DISTINCT code FROM ak_stock_zh_a_spot_em WHERE code IS NOT NULL"
                )
            )
            codes = [row[0] for row in result.fetchall()]

            print(f"找到 {len(codes)} 个股票代码")

            if not codes:
                print(
                    "警告：没有找到任何股票代码，请确保 ak_stock_zh_a_spot_em 表中有数据"
                )
                return "No stock codes found in database"

            # 为每个代码创建3个任务
            task_count = 0
            successful_codes = 0

            for i, code in enumerate(codes, 1):
                if code and code.strip():  # 确保代码不为空且不是空白字符
                    try:
                        # 创建不复权任务
                        task1 = celery_app.send_task(
                            "app.tasks.akshare_tasks.fetch_and_store_stock_zh_a_hist_daily",
                            args=[code, ""],
                            queue="akshare_queue",
                        )
                        task_count += 1

                        # 创建前复权任务
                        task2 = celery_app.send_task(
                            "app.tasks.akshare_tasks.fetch_and_store_stock_zh_a_hist_daily",
                            args=[code, "qfq"],
                            queue="akshare_queue",
                        )
                        task_count += 1

                        # 创建后复权任务
                        task3 = celery_app.send_task(
                            "app.tasks.akshare_tasks.fetch_and_store_stock_zh_a_hist_daily",
                            args=[code, "hfq"],
                            queue="akshare_queue",
                        )
                        task_count += 1

                        successful_codes += 1
                        print(
                            f"[{i}/{len(codes)}] 已为股票 {code} 创建3个任务 (IDs: {task1.id[:8]}, {task2.id[:8]}, {task3.id[:8]})"
                        )

                    except Exception as e:
                        print(f"为股票 {code} 创建任务时出错: {str(e)}")
                else:
                    print(f"跳过无效的股票代码: '{code}'")

            print(f"\n任务创建完成！")
            print(f"- 处理的股票数量: {successful_codes}/{len(codes)}")
            print(f"- 总共创建的任务数量: {task_count}")
            print(f"- 每个股票创建3个任务：不复权、前复权、后复权")

            return (
                f"Successfully created {task_count} tasks for {successful_codes} stocks"
            )

        finally:
            db.close()

    except Exception as e:
        error_msg = f"Error creating tasks: {str(e)}"
        print(f"错误: {error_msg}")
        return error_msg


def main():
    """主函数"""
    print("=" * 60)
    print("创建所有股票历史数据获取任务")
    print("=" * 60)

    result = create_all_stock_hist_tasks()

    print("\n" + "=" * 60)
    print(f"执行结果: {result}")
    print("=" * 60)


if __name__ == "__main__":
    main()
