from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config import settings

engine = create_engine(
    settings.database_url,
    echo=False,  # 将echo设置为False以禁用SQL日志
    future=True,
    pool_size=10,  # 连接池大小
    max_overflow=20,  # 超过 pool_size 后允许的最大连接数
    pool_timeout=30,  # 获取连接的超时时间（秒）
)

SessionLocal = sessionmaker(bind=engine, expire_on_commit=False)

Base = declarative_base()


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
