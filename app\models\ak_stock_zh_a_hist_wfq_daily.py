from sqlalchemy import BigInteger, Column, Float, String, text
from sqlalchemy.dialects.postgresql import TIMESTAMP

from app.models.base import Base


class AkStockZhAHistWFQDaily(Base):
    """
    A股历史行情（无复权，日线）
    """

    __tablename__ = "ak_stock_zh_a_hist_wfq_daily"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    date = Column(String, nullable=False, comment="交易日")
    code = Column(String, nullable=False, comment="不带市场标识的股票代码")
    open = Column(Float, comment="开盘价")
    close = Column(Float, comment="收盘价")
    high = Column(Float, comment="最高价")
    low = Column(Float, comment="最低价")
    volume = Column(BigInteger, comment="成交量 (手)")
    turnover = Column(Float, comment="成交额 (元)")
    amplitude = Column(Float, comment="振幅 (%)")
    change_percent = Column(Float, comment="涨跌幅 (%)")
    change_amount = Column(Float, comment="涨跌额 (元)")
    turnover_rate = Column(Float, comment="换手率 (%)")
    inserted_at = Column(
        TIMESTAMP(timezone=True),
        server_default=text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
        nullable=False,
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        server_default=text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
        onupdate=text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
        nullable=False,
    )

    def __repr__(self):
        return f"<AkStockZhAHistWFQDaily(code='{self.code}', date='{self.date}')>"
