"""ak_stock_zh_a_hist_hfq_daily

Revision ID: 20250617_223120
Revises: 20250617_223119
Create Date: 2025-06-17 22:35:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250617_223120"
down_revision: Union[str, None] = "20250617_223119"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create ak_stock_zh_a_hist_hfq_daily table."""
    op.create_table(
        "ak_stock_zh_a_hist_hfq_daily",
        sa.Column(
            "id", sa.BigInteger(), primary_key=True, autoincrement=True, nullable=False
        ),
        sa.Column("date", sa.String(), nullable=False, comment="交易日"),
        sa.Column(
            "code", sa.String(), nullable=False, comment="不带市场标识的股票代码"
        ),
        sa.Column("open", sa.Float(), nullable=True, comment="开盘价"),
        sa.Column("close", sa.Float(), nullable=True, comment="收盘价"),
        sa.Column("high", sa.Float(), nullable=True, comment="最高价"),
        sa.Column("low", sa.Float(), nullable=True, comment="最低价"),
        sa.Column("volume", sa.BigInteger(), nullable=True, comment="成交量 (手)"),
        sa.Column("turnover", sa.Float(), nullable=True, comment="成交额 (元)"),
        sa.Column("amplitude", sa.Float(), nullable=True, comment="振幅 (%)"),
        sa.Column("change_percent", sa.Float(), nullable=True, comment="涨跌幅 (%)"),
        sa.Column("change_amount", sa.Float(), nullable=True, comment="涨跌额 (元)"),
        sa.Column("turnover_rate", sa.Float(), nullable=True, comment="换手率 (%)"),
        sa.Column(
            "inserted_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
            onupdate=sa.text("(CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Shanghai')"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code"),
        "ak_stock_zh_a_hist_hfq_daily",
        ["code"],
        unique=False,
    )


def downgrade() -> None:
    """Drop ak_stock_zh_a_hist_hfq_daily table."""
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code"),
        table_name="ak_stock_zh_a_hist_hfq_daily",
    )
    op.drop_table("ak_stock_zh_a_hist_hfq_daily")
