import akshare as ak


def fetch_stock_zh_a_spot_em():
    """
    获取A股实时行情数据 (来源: 东方财富)
    沪深京A股
    """
    import time

    max_retries = 3
    retry_delay = 30

    for attempt in range(1, max_retries + 1):
        try:
            df = ak.stock_zh_a_spot_em()
            if not df.empty:
                return df
            return None
        except Exception as e:
            print(f"Error fetching A股实时行情数据 (尝试 {attempt}/{max_retries}): {e}")
            if attempt < max_retries:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("已达到最大重试次数，放弃获取数据")
                return None


def fetch_stock_zh_a_hist_daily(symbol, start_date, end_date, adjust=""):
    """
    获取A股历史行情数据 (来源: 东方财富)
    :param symbol: 股票代码，如 '600859'
    :param start_date: 开始日期，格式 'YYYYMMDD'
    :param end_date: 结束日期，格式 'YYYYMMDD'
    :param adjust: 复权类型，"qfq" 前复权，"hfq" 后复权，"" 不复权
    :return: DataFrame 或 None
    """
    import time

    max_retries = 3
    retry_delay = 30

    for attempt in range(1, max_retries + 1):
        try:
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=adjust,
                timeout=60,
            )
            if df is not None and not df.empty:
                return df
            return None
        except Exception as e:
            print(f"Error fetching A股历史行情数据 (尝试 {attempt}/{max_retries}): {e}")
            if attempt < max_retries:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("已达到最大重试次数，放弃获取数据")
                return None


if __name__ == "__main__":
    pass
    # stock_zh_a_hist_df = fetch_stock_zh_a_hist_daily(
    #     "600859", "19800101", "20250621", "hfq"
    # )
    # print(stock_zh_a_hist_df)
