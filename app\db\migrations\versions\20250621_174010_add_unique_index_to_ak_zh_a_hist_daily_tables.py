"""add_unique_index_to_ak_zh_a_hist_daily_tables

Revision ID: 20250621_174010
Revises: 20250617_223120
Create Date: 2025-06-21 17:40:10.741801

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250621_174010"
down_revision: Union[str, None] = "20250617_223120"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add unique indices to history tables."""
    # 1. 表ak_stock_zh_a_hist_wfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_wfq_daily_code"),
        table_name="ak_stock_zh_a_hist_wfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_wfq_daily_code_date"),
        "ak_stock_zh_a_hist_wfq_daily",
        ["code", "date"],
        unique=True,
    )

    # 2. 表ak_stock_zh_a_hist_qfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_qfq_daily_code"),
        table_name="ak_stock_zh_a_hist_qfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_qfq_daily_code_date"),
        "ak_stock_zh_a_hist_qfq_daily",
        ["code", "date"],
        unique=True,
    )

    # 3. 表ak_stock_zh_a_hist_hfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code"),
        table_name="ak_stock_zh_a_hist_hfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code_date"),
        "ak_stock_zh_a_hist_hfq_daily",
        ["code", "date"],
        unique=True,
    )


def downgrade() -> None:
    """Restore original indices."""
    # 1. 表ak_stock_zh_a_hist_wfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_wfq_daily_code_date"),
        table_name="ak_stock_zh_a_hist_wfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_wfq_daily_code"),
        "ak_stock_zh_a_hist_wfq_daily",
        ["code"],
        unique=False,
    )

    # 2. 表ak_stock_zh_a_hist_qfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_qfq_daily_code_date"),
        table_name="ak_stock_zh_a_hist_qfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_qfq_daily_code"),
        "ak_stock_zh_a_hist_qfq_daily",
        ["code"],
        unique=False,
    )

    # 3. 表ak_stock_zh_a_hist_hfq_daily
    op.drop_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code_date"),
        table_name="ak_stock_zh_a_hist_hfq_daily",
    )
    op.create_index(
        op.f("ix_ak_stock_zh_a_hist_hfq_daily_code"),
        "ak_stock_zh_a_hist_hfq_daily",
        ["code"],
        unique=False,
    )
