from app.models.ak_stock_zh_a_hist_hfq_daily import AkStockZhAHistHFQDaily
from app.models.ak_stock_zh_a_hist_qfq_daily import AkStockZhAHistQFQDaily
from app.models.ak_stock_zh_a_hist_wfq_daily import AkStockZhAHistWFQDaily
from app.models.ak_stock_zh_a_spot_em import AkStockZhASpotEm
from app.models.base import Base

# 在这里导入其他模型
# from app.models.other_model import OtherModel

# 导出所有模型，使它们可以通过 app.models 访问
__all__ = [
    "Base",
    "AkStockZhASpotEm",
    "AkStockZhAHistWFQDaily",
    "AkStockZhAHistQFQDaily",
    "AkStockZhAHistHFQDaily",
]
