# cn-backtrader

### 计划：
- 实现AKShare多股票并行抓取
  - AKShare请求频率限制
- 数据清洗模块（处理停牌/异常数据）
- 分析数据，辅助选择股票
- 应用量化策略

### 启动Web
poetry run uvicorn app.main:app --reload

### 启动celery
- login WSL Ubuntu
  - poetry run celery -A app.celery_app worker -P gevent -l info -Q akshare_queue -c 4
  - poetry run celery -A app.celery_app beat -l info
- in Win11
  - poetry run celery -A app.celery_app worker -P solo -l info -Q akshare_queue
  - poetry run celery -A app.celery_app beat -l info

### 数据库
1. 空数据库初始化
poetry run alembic stamp head

2. 创建新迁移
poetry run alembic revision --autogenerate -m "create ak_stock_zh_a_spot_em"
poetry run python scripts/create_migration.py "create ak_stock_zh_a_spot_em"

3. 迁移到最新版本
poetry run alembic upgrade head

4. 查看历史迁移
poetry run alembic history

5. 查看当前迁移
poetry run alembic current

### 触发task
poetry run python scripts/trigger_akshare_task.py

### docker
$env:DOCKER_BUILDKIT=1; docker build -t cn-backtrader:v2 .

docker tag cn-backtrader:v2 sgp.vultrcr.com/ys118/cn-backtrader:v2

docker push sgp.vultrcr.com/ys118/cn-backtrader:v2

docker pull sgp.vultrcr.com/ys118/cn-backtrader:v2

docker run -d \
  --name cn-backtrader \
  -p 8000:8000 \
  --restart=on-failure \
  --add-host=host.docker.internal:host-gateway \
  -e DATABASE_URL="postgresql+psycopg2://用户名:密码@host.docker.internal:5432/cn-backtrader" \
  -e CELERY_BROKER_URL="redis://host.docker.internal:6379/0" \
  -e CELERY_RESULT_BACKEND="redis://host.docker.internal:6379/0" \
  -e REDIS_URL="redis://host.docker.internal:6379/0" \
  cn-backtrader:v5

### 查看docker日志
docker logs -f cn-backtrader

### 进入容器
docker exec -it cn-backtrader sh

### 查看日志文件
cat /tmp/logs/web.log
cat /tmp/logs/celery.log
cat /tmp/logs/celery_beat.log

### 实时查看日志更新
tail -f /tmp/logs/web.log
tail -f /tmp/logs/celery.log
tail -f /tmp/logs/celery_beat.log
